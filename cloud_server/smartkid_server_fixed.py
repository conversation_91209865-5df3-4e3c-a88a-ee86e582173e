#!/usr/bin/env python3
"""
SmartKid WebSocket服务器 - 修复版
完全仿照simple.py的音频编码发送实现
"""

import asyncio
import websockets
import json
import logging
import time
import uuid
import os
import struct
import wave
import opuslib
import numpy as np
from typing import Dict, Any, Optional, Set
from datetime import datetime
from config import config, TEST_COMMANDS, get_operation_description, is_valid_operation

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('SmartKidServer')

class SmartKidServer:
    def __init__(self, host='localhost', port=8765):
        self.host = host
        self.port = port
        self.clients = {}  # 存储连接的客户端
        self.session_counter = 1
        self.audio_folder = "audio"  # 音频文件夹路径
        self.current_audio_tasks = {}  # 存储当前播放的音频任务
        self.music_playlist = []  # 音乐播放列表
        self.current_music_index = {}  # 每个客户端当前播放的音乐索引
        
        # 初始化Opus编码器
        try:
            # 16kHz采样率，单声道，VoIP应用
            self.opus_encoder = opuslib.Encoder(16000, 1, opuslib.APPLICATION_VOIP)
            logger.info("✓ Opus编码器初始化成功 (16kHz, 单声道)")
        except Exception as e:
            logger.error(f"✗ Opus编码器初始化失败: {e}")
            self.opus_encoder = None

    def generate_session_id(self) -> str:
        """生成会话ID"""
        return str(uuid.uuid4()).replace('-', '')

    async def register_client(self, websocket, path):
        """注册新客户端"""
        client_id = f"client_{len(self.clients) + 1}"
        self.clients[client_id] = {
            'websocket': websocket,
            'connected_at': datetime.now(),
            'last_activity': datetime.now()
        }
        logger.info(f"=== CLIENT REGISTRATION ===")
        logger.info(f"Client ID: {client_id}")
        logger.info(f"Remote address: {websocket.remote_address}")
        logger.info(f"Path: {path}")
        logger.info(f"Total clients: {len(self.clients)}")
        logger.info("=== END CLIENT REGISTRATION ===")
        return client_id

    async def unregister_client(self, client_id):
        """注销客户端"""
        if client_id in self.clients:
            logger.info(f"=== CLIENT UNREGISTRATION ===")
            logger.info(f"Client ID: {client_id}")
            logger.info(f"Connected at: {self.clients[client_id]['connected_at']}")
            logger.info(f"Last activity: {self.clients[client_id]['last_activity']}")
            
            # 清理音频任务
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                del self.current_audio_tasks[client_id]
                logger.info(f"Cancelled audio task for {client_id}")
            
            del self.clients[client_id]
            logger.info(f"Remaining clients: {len(self.clients)}")
            logger.info("=== END CLIENT UNREGISTRATION ===")
        else:
            logger.warning(f"Attempted to unregister unknown client: {client_id}")

    def create_response(self, action: str, operate: str, text: str = "", 
                       code: int = 2200, message: str = "SUCCEED") -> Dict[str, Any]:
        """创建标准响应格式"""
        session_id = self.generate_session_id()
        response = {
            "code": code,
            "data": {
                "action": action,
                "operate": operate,
                "text": text
            },
            "message": message,
            "sessionId": session_id,
            "sn": self.session_counter
        }
        self.session_counter += 1
        return response

    def load_music_playlist(self):
        """加载音乐播放列表"""
        self.music_playlist = []
        
        if not os.path.exists(self.audio_folder):
            logger.warning(f"Audio folder not found: {self.audio_folder}")
            return
        
        # 扫描audio文件夹中的所有WAV文件
        for filename in os.listdir(self.audio_folder):
            if filename.lower().endswith('.wav'):
                file_path = os.path.join(self.audio_folder, filename)
                if os.path.isfile(file_path):
                    # 获取文件信息
                    file_size = os.path.getsize(file_path)
                    music_info = {
                        'name': os.path.splitext(filename)[0],
                        'filename': filename,
                        'path': file_path,
                        'size': file_size
                    }
                    self.music_playlist.append(music_info)
        
        # 按文件名排序
        self.music_playlist.sort(key=lambda x: x['name'])
        
        logger.info(f"Loaded music playlist with {len(self.music_playlist)} songs:")
        for i, music in enumerate(self.music_playlist):
            logger.info(f"  {i+1}. {music['name']} ({music['size']} bytes)")

    def get_current_music(self, client_id: str) -> Optional[Dict[str, Any]]:
        """获取当前应该播放的音乐"""
        if not self.music_playlist:
            self.load_music_playlist()
        
        if not self.music_playlist:
            logger.error("No music files found in playlist")
            return None
        
        # 如果客户端没有当前索引，从0开始
        if client_id not in self.current_music_index:
            self.current_music_index[client_id] = 0
        
        index = self.current_music_index[client_id]
        
        # 确保索引在有效范围内
        if index >= len(self.music_playlist):
            index = 0
            self.current_music_index[client_id] = 0
        
        current_music = self.music_playlist[index]
        logger.info(f"Current music for {client_id}: {current_music['name']} (index {index})")
        
        return current_music

    def next_music(self, client_id: str) -> Optional[Dict[str, Any]]:
        """切换到下一首音乐"""
        if not self.music_playlist:
            return None
        
        if client_id not in self.current_music_index:
            self.current_music_index[client_id] = 0
        
        # 移动到下一首
        self.current_music_index[client_id] = (self.current_music_index[client_id] + 1) % len(self.music_playlist)
        
        return self.get_current_music(client_id)

    def prev_music(self, client_id: str) -> Optional[Dict[str, Any]]:
        """切换到上一首音乐"""
        if not self.music_playlist:
            return None
        
        if client_id not in self.current_music_index:
            self.current_music_index[client_id] = 0
        
        # 移动到上一首
        self.current_music_index[client_id] = (self.current_music_index[client_id] - 1) % len(self.music_playlist)
        
        return self.get_current_music(client_id)

    def resample_audio(self, audio_data, orig_sr, target_sr):
        """简单的重采样（线性插值）- 完全仿照simple.py"""
        if orig_sr == target_sr:
            return audio_data

        # 计算重采样比例
        ratio = target_sr / orig_sr
        new_length = int(len(audio_data) * ratio)

        # 线性插值重采样
        old_indices = np.linspace(0, len(audio_data) - 1, new_length)
        new_audio = np.interp(old_indices, np.arange(len(audio_data)), audio_data)

        return new_audio.astype(audio_data.dtype)

    def load_wav_file_simple(self, file_path: str):
        """加载WAV文件 - 完全仿照simple.py的实现"""
        try:
            with wave.open(file_path, 'rb') as wav:
                sample_rate = wav.getframerate()
                channels = wav.getnchannels()
                sample_width = wav.getsampwidth()

                logger.info(f"WAV文件信息:")
                logger.info(f"  文件: {file_path}")
                logger.info(f"  采样率: {sample_rate} Hz")
                logger.info(f"  声道数: {channels}")
                logger.info(f"  位深: {sample_width * 8} bit")

                # 读取所有音频数据
                frames = wav.readframes(wav.getnframes())

                # 转换为numpy数组
                if sample_width == 1:
                    dtype = np.uint8
                elif sample_width == 2:
                    dtype = np.int16
                else:
                    dtype = np.int32

                audio_data = np.frombuffer(frames, dtype=dtype)

                # 如果是立体声，转换为单声道
                if channels == 2:
                    audio_data = audio_data.reshape(-1, 2)
                    audio_data = np.mean(audio_data, axis=1).astype(dtype)

                # 重采样到16kHz（如果需要）
                if sample_rate != 16000:
                    audio_data = self.resample_audio(audio_data, sample_rate, 16000)

                logger.info(f"  处理后: 16000 Hz, 单声道, {len(audio_data)} 采样点")
                logger.info(f"  时长: {len(audio_data) / 16000:.2f} 秒")

                return audio_data

        except Exception as e:
            logger.error(f"加载WAV文件失败: {file_path} - {e}")
            return None

    def create_audio_packet(self, opus_data: bytes, seq_num: int) -> bytes:
        """创建WebSocket音频包（完全仿照simple.py的格式）"""
        version = 0x01
        audio_type = 0x01
        payload_len = len(opus_data)
        reserved = 0x00
        
        # 打包头部（小端序，与ESP32一致）
        header = struct.pack('<BBHHH', 
                           version,        # version
                           audio_type,     # type
                           seq_num & 0xFFFF,  # seqNum
                           payload_len,    # payloadLen
                           reserved)       # resv
        
        return header + opus_data

    def encode_to_opus(self, pcm_frame):
        """使用真正的Opus编码器编码PCM数据 - 完全仿照simple.py"""
        if not self.opus_encoder:
            logger.error("Opus编码器不可用")
            return None

        try:
            # 确保输入是320个样本的int16数组
            if len(pcm_frame) != 320:
                logger.warning(f"帧大小不正确 {len(pcm_frame)}, 期望320")
                return None

            # Opus编码 - 输入必须是bytes格式
            pcm_bytes = pcm_frame.astype(np.int16).tobytes()
            opus_data = self.opus_encoder.encode(pcm_bytes, 320)

            if len(opus_data) > 0:
                return opus_data
            else:
                logger.warning("Opus编码返回空数据")
                return None

        except Exception as e:
            logger.error(f"Opus编码失败: {e}")
            return None

    async def send_opus_audio_stream_by_path(self, websocket, client_id: str, audio_path: str, music_name: str):
        """使用opuslib发送指定路径的Opus编码音频流 - 完全仿照simple.py实现"""
        try:
            if not os.path.exists(audio_path):
                logger.error(f"Audio file not found: {audio_path}")
                return

            logger.info(f"=== SENDING OPUS AUDIO STREAM TO {client_id} ===")
            logger.info(f"Music: {music_name}")
            logger.info(f"Audio file: {audio_path}")

            # 加载WAV文件（仿照simple.py）
            audio_data = self.load_wav_file_simple(audio_path)
            if audio_data is None:
                logger.error(f"Failed to load audio file: {audio_path}")
                return

            # 音频处理参数（完全仿照simple.py）
            frame_size = 320  # 16kHz * 0.02s = 320 samples per 20ms frame
            seq_num = 0
            frame_count = 0
            bytes_sent = 0

            logger.info(f"开始发送音频流，总样本数: {len(audio_data)}")

            # 分帧处理（完全仿照simple.py的逻辑）
            for i in range(0, len(audio_data), frame_size):
                # 获取一帧音频数据
                frame = audio_data[i:i + frame_size]

                # 确保帧大小正确
                if len(frame) < frame_size:
                    # 如果不足一帧，用零填充
                    padded_frame = np.zeros(frame_size, dtype=np.int16)
                    padded_frame[:len(frame)] = frame.astype(np.int16)
                    frame = padded_frame
                else:
                    frame = frame.astype(np.int16)

                # 使用真正的Opus编码
                opus_data = self.encode_to_opus(frame)

                if opus_data:
                    # 创建WebSocket音频包（仿照simple.py格式）
                    packet = self.create_audio_packet(opus_data, seq_num)

                    # 发送二进制数据
                    await websocket.send(packet)

                    bytes_sent += len(packet)
                    frame_count += 1
                    seq_num += 1

                    # 每50帧记录一次进度（每秒一次）
                    if frame_count % 50 == 0:
                        logger.info(f"Sent {frame_count} audio packets, {bytes_sent} bytes")

                    # 20ms间隔，模拟实时音频流（完全仿照simple.py）
                    await asyncio.sleep(0.02)
                else:
                    logger.warning(f"Opus编码失败，跳过帧 {frame_count}")

            logger.info(f"Opus audio stream sent successfully: {frame_count} packets, {bytes_sent} bytes")
            logger.info("=== END SENDING OPUS AUDIO STREAM ===")

        except Exception as e:
            logger.error(f"Error sending Opus audio stream: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    async def handle_music_command(self, data: Dict[str, Any], websocket, client_id: str) -> Dict[str, Any]:
        """处理音乐命令"""
        operate = data.get('operate', '')
        body = data.get('body', '')
        request_id = data.get('requestId', '')
        timestamp = data.get('timestamp', '')

        logger.info(f"=== HANDLING MUSIC COMMAND ===")
        logger.info(f"Full data: {data}")
        logger.info(f"Operate: '{operate}'")
        logger.info(f"Body: '{body}'")
        logger.info(f"RequestId: '{request_id}'")
        logger.info(f"Timestamp: '{timestamp}'")

        if operate == 'start':
            logger.info(f"Processing music start command")

            # 获取当前应该播放的音乐
            current_music = self.get_current_music(client_id)
            if not current_music:
                logger.error("No music available to play")
                return self.create_response('music', 'start', '没有可播放的音乐文件',
                                          code=4004, message='ERROR')

            music_name = current_music['name']
            music_path = current_music['path']

            response = self.create_response('music', 'start', f'开始播放音乐: {music_name}')
            logger.info(f"Created response for music start: {response}")

            # 启动Opus音频流发送任务
            logger.info(f"Starting Opus audio stream for: '{music_name}' ({music_path})")

            # 取消之前的音频任务（如果存在）
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                logger.info(f"Cancelled previous audio task for {client_id}")

            # 创建新的Opus音频流发送任务
            audio_task = asyncio.create_task(
                self.send_opus_audio_stream_by_path(websocket, client_id, music_path, music_name)
            )
            self.current_audio_tasks[client_id] = audio_task

            return response

        elif operate == 'pause':
            logger.info(f"Processing music pause command")
            # 暂停音频发送任务
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                logger.info(f"Paused audio task for {client_id}")
            response = self.create_response('music', 'pause', '暂停音乐')
            logger.info(f"Created response for music pause: {response}")
            return response

        elif operate == 'resume':
            logger.info(f"Processing music resume command")

            # 恢复当前音乐的播放
            current_music = self.get_current_music(client_id)
            if not current_music:
                return self.create_response('music', 'resume', '没有可恢复的音乐',
                                          code=4004, message='ERROR')

            music_name = current_music['name']
            music_path = current_music['path']

            audio_task = asyncio.create_task(
                self.send_opus_audio_stream_by_path(websocket, client_id, music_path, music_name)
            )
            self.current_audio_tasks[client_id] = audio_task
            response = self.create_response('music', 'resume', f'恢复音乐: {music_name}')
            logger.info(f"Created response for music resume: {response}")
            return response

        elif operate == 'stop':
            logger.info(f"Processing music stop command")
            # 停止音频发送任务
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                del self.current_audio_tasks[client_id]
                logger.info(f"Stopped audio task for {client_id}")
            response = self.create_response('music', 'stop', '停止音乐')
            logger.info(f"Created response for music stop: {response}")
            return response

        elif operate == 'next':
            logger.info(f"Processing music next command")

            # 切换到下一首音乐
            next_music = self.next_music(client_id)
            if not next_music:
                return self.create_response('music', 'next', '没有下一首音乐',
                                          code=4004, message='ERROR')

            music_name = next_music['name']
            music_path = next_music['path']

            # 停止当前播放
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                logger.info(f"Stopped current music for next")

            # 开始播放下一首
            audio_task = asyncio.create_task(
                self.send_opus_audio_stream_by_path(websocket, client_id, music_path, music_name)
            )
            self.current_audio_tasks[client_id] = audio_task

            response = self.create_response('music', 'next', f'下一首音乐: {music_name}')
            logger.info(f"Created response for music next: {response}")
            return response

        elif operate == 'prev':
            logger.info(f"Processing music prev command")

            # 切换到上一首音乐
            prev_music = self.prev_music(client_id)
            if not prev_music:
                return self.create_response('music', 'prev', '没有上一首音乐',
                                          code=4004, message='ERROR')

            music_name = prev_music['name']
            music_path = prev_music['path']

            # 停止当前播放
            if client_id in self.current_audio_tasks:
                self.current_audio_tasks[client_id].cancel()
                logger.info(f"Stopped current music for prev")

            # 开始播放上一首
            audio_task = asyncio.create_task(
                self.send_opus_audio_stream_by_path(websocket, client_id, music_path, music_name)
            )
            self.current_audio_tasks[client_id] = audio_task

            response = self.create_response('music', 'prev', f'上一首音乐: {music_name}')
            logger.info(f"Created response for music prev: {response}")
            return response
        else:
            logger.warning(f"Unknown music operate: '{operate}'")
            response = self.create_response('music', operate, f'未知音乐操作: {operate}',
                                      code=4000, message='ERROR')
            logger.info(f"Created error response for unknown music operate: {response}")
            return response

    async def process_message(self, message: str, websocket, client_id: str) -> Optional[Dict[str, Any]]:
        """处理接收到的消息"""
        try:
            logger.info(f"=== PROCESSING MESSAGE ===")
            logger.info(f"Raw message: {message}")

            data = json.loads(message)
            logger.info(f"Parsed JSON data: {data}")

            action = data.get('action', '')
            operate = data.get('operate', '')
            logger.info(f"Extracted action: '{action}', operate: '{operate}'")

            if action == 'music':
                logger.info(f"Routing to music handler")
                response = await self.handle_music_command(data, websocket, client_id)
                logger.info(f"Music handler returned: {response}")
                return response
            elif action == 'test':
                logger.info(f"Routing to test handler")
                response = self.create_response('test', data.get('operate', 'test'), '测试响应')
                logger.info(f"Test handler returned: {response}")
                return response
            else:
                logger.warning(f"Unknown action: '{action}'")
                response = self.create_response('error', 'unknown', f'未知动作: {action}',
                                          code=4000, message='ERROR')
                logger.info(f"Error response for unknown action: {response}")
                return response

        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            logger.error(f"Failed to parse message: {repr(message)}")
            response = self.create_response('error', 'parse', 'JSON解析错误',
                                      code=4000, message='ERROR')
            logger.info(f"JSON error response: {response}")
            return response
        except Exception as e:
            logger.error(f"Process message error: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            response = self.create_response('error', 'internal', '内部服务器错误',
                                      code=5000, message='ERROR')
            logger.info(f"Internal error response: {response}")
            return response
        finally:
            logger.info(f"=== END PROCESSING MESSAGE ===")

    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        client_id = await self.register_client(websocket, path)

        try:
            logger.info(f"=== CLIENT {client_id} READY TO RECEIVE MESSAGES ===")
            async for message in websocket:
                logger.info(f"=== RAW MESSAGE RECEIVED FROM {client_id} ===")
                logger.info(f"Message type: {type(message)}")
                logger.info(f"Message length: {len(message)}")
                logger.info(f"Message content (repr): {repr(message)}")
                logger.info(f"Message content (str): {message}")
                logger.info("=== END RAW MESSAGE ===")

                # 更新最后活动时间
                if client_id in self.clients:
                    self.clients[client_id]['last_activity'] = datetime.now()
                    logger.info(f"Updated last activity for {client_id}")

                # 处理消息
                logger.info(f"Starting to process message from {client_id}")
                response = await self.process_message(message, websocket, client_id)
                logger.info(f"Message processing completed for {client_id}")

                if response:
                    response_json = json.dumps(response, ensure_ascii=False)
                    logger.info(f"=== SENDING RESPONSE TO {client_id} ===")
                    logger.info(f"Response: {response_json}")
                    await websocket.send(response_json)
                    logger.info(f"Response sent successfully to {client_id}")
                    logger.info("=== END RESPONSE ===")
                else:
                    logger.warning(f"No response generated for message from {client_id}")

        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client {client_id} connection closed normally")
        except Exception as e:
            logger.error(f"Error handling client {client_id}: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
        finally:
            await self.unregister_client(client_id)
            logger.info(f"Client {client_id} cleanup completed")

    async def start_server(self):
        """启动服务器"""
        logger.info(f"Starting SmartKid server on {self.host}:{self.port}")

        # 加载音乐播放列表
        self.load_music_playlist()

        async with websockets.serve(self.handle_client, self.host, self.port):
            logger.info(f"SmartKid server is running on ws://{self.host}:{self.port}")
            await asyncio.Future()  # 保持服务器运行

def main():
    """主函数"""
    server = SmartKidServer(host='************', port=8768)

    try:
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")

if __name__ == "__main__":
    main()
